"""
用户仓储实现

负责用户数据的持久化操作，遵循DDD架构模式
"""

from abc import ABC, abstractmethod
from typing import Optional, List
from src.db.connection import execute_db_query, Database
from src.models.user_info_class import User
from src.utils.logger import logger


class UserRepository(ABC):
    """用户仓储接口"""
    
    @abstractmethod
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        pass
    
    @abstractmethod
    def find_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查找用户"""
        pass
    
    @abstractmethod
    def find_by_open_id(self, open_id: str) -> Optional[User]:
        """根据open_id查找用户"""
        pass
    
    @abstractmethod
    def search_users(self, search_term: str, limit: int = 5) -> List[User]:
        """模糊搜索用户"""
        pass
    
    @abstractmethod
    def list_users(self, limit: int = 10) -> List[User]:
        """获取用户列表"""
        pass

    @abstractmethod
    def is_admin_by_union_id(self, union_id: str) -> bool:
        """根据union_id检查用户是否为管理员"""
        pass

    @abstractmethod
    def find_full_user_info_by_open_id(self, open_id: str) -> Optional[dict]:
        """根据open_id获取完整的用户信息（包括额外字段）"""
        pass

    @abstractmethod
    def update_user_folder_info(self, open_id: str, folder_token: str, folder_name: str) -> bool:
        pass

    @abstractmethod
    def get_user_folder_info(self, open_id: str) -> Optional[dict]:
        pass

    @abstractmethod
    def update_user_resigned_status(self, open_id: str, is_resigned: bool = True) -> bool:
        """更新用户的离职状态"""
        pass


class ChatbiUserRepository(UserRepository):
    """ChatBI数据库用户仓储实现"""
    
    def _build_user_from_result(self, result: dict) -> User:
        """从数据库结果构建用户实体"""
        return User(
            name=result['name'],
            email=result['email'],
            open_id=result['open_id'],
            avatar_url=result.get('avatar'),
            user_id=result.get('user_id'),
            job_title=result.get('job_title'),
            department=result.get('first_level_department'),
        )
    
    def _get_base_query(self) -> str:
        """获取基础查询SQL"""
        return """
            SELECT 
                name, 
                email, 
                open_id,
                avatar,
                user_id,
                job_title,
                first_level_department
            FROM users
        """
    
    def find_by_username(self, username: str) -> Optional[User]:
        """根据用户名查找用户"""
        try:
            sql = self._get_base_query() + " WHERE name = %s LIMIT 1"
            result = execute_db_query(sql, params=(username,), fetch='one', database=Database.CHATBI)
            
            if result:
                user = self._build_user_from_result(result)
                logger.info(f"✅ 找到用户: {user.name} ({user.email})")
                return user
            else:
                logger.warning(f"❌ 未找到用户: {username}")
                return None
                
        except Exception as e:
            logger.exception(f"根据用户名查找用户失败 {username}: {e}")
            return None
    
    def find_by_email(self, email: str) -> Optional[User]:
        """根据邮箱查找用户"""
        try:
            sql = self._get_base_query() + " WHERE email = %s LIMIT 1"
            result = execute_db_query(sql, params=(email,), fetch='one', database=Database.CHATBI)
            
            if result:
                user = self._build_user_from_result(result)
                logger.info(f"✅ 找到用户邮箱: {user.email} -> {user.name}")
                return user
            else:
                logger.warning(f"❌ 未找到邮箱: {email}")
                return None
                
        except Exception as e:
            logger.exception(f"根据邮箱查找用户失败 {email}: {e}")
            return None
    
    def find_by_open_id(self, open_id: str) -> Optional[User]:
        """根据open_id查找用户"""
        try:
            sql = self._get_base_query() + " WHERE open_id = %s LIMIT 1"
            result = execute_db_query(sql, params=(open_id,), fetch='one', database=Database.CHATBI)
            
            if result:
                user = self._build_user_from_result(result)
                logger.info(f"✅ 找到用户open_id: {open_id} -> {user.name}")
                return user
            else:
                logger.warning(f"❌ 未找到open_id: {open_id}")
                return None
                
        except Exception as e:
            logger.exception(f"根据open_id查找用户失败 {open_id}: {e}")
            return None
    
    def search_users(self, search_term: str, limit: int = 5) -> List[User]:
        """模糊搜索用户"""
        try:
            sql = self._get_base_query() + " WHERE name LIKE %s OR email LIKE %s LIMIT %s"
            like_pattern = f"%{search_term}%"
            results = execute_db_query(
                sql, 
                params=(like_pattern, like_pattern, limit), 
                fetch='all', 
                database=Database.CHATBI
            ) or []
            
            users = []
            for result in results:
                users.append(self._build_user_from_result(result))
            
            logger.info(f"🔍 搜索 '{search_term}' 找到 {len(users)} 个用户")
            return users
            
        except Exception as e:
            logger.exception(f"搜索用户失败: {e}")
            return []
    
    def list_users(self, limit: int = 10) -> List[User]:
        """获取用户列表"""
        try:
            sql = self._get_base_query() + " LIMIT %s"
            results = execute_db_query(sql, params=(limit,), fetch='all', database=Database.CHATBI) or []
            
            users = []
            for result in results:
                users.append(self._build_user_from_result(result))
            
            logger.info(f"📋 获取用户列表，共 {len(users)} 个用户")
            return users
            
        except Exception as e:
            logger.exception(f"获取用户列表失败: {e}")
            return []

    def is_admin_by_union_id(self, union_id: str) -> bool:
        """根据union_id检查用户是否为管理员"""
        if not union_id:
            return False

        try:
            sql = "SELECT is_admin FROM users WHERE union_id = %s"
            result = execute_db_query(sql, (union_id,), fetch='one', database=Database.CHATBI)

            if result:
                return bool(result.get('is_admin', 0))
            else:
                logger.warning(f"❌ 未找到union_id: {union_id}的用户")
                return False

        except Exception as e:
            logger.exception(f"根据union_id检查管理员权限失败 {union_id}: {e}")
            return False

    def find_full_user_info_by_open_id(self, open_id: str) -> Optional[dict]:
        """根据open_id获取完整的用户详细信息（包括额外的字段如id, union_id, avatar, last_login_time等）"""
        if not open_id:
            logger.warning("open_id为空，无法查询用户信息")
            return None

        try:
            full_sql = """
                SELECT
                    id,
                    name,
                    email,
                    user_id,
                    job_title,
                    open_id,
                    union_id,
                    avatar,
                    first_level_department,
                    is_admin,
                    last_login_time
                FROM users
                WHERE open_id = %s
                LIMIT 1
            """
            result = execute_db_query(full_sql, (open_id,), fetch='one', database=Database.CHATBI)

            if result:
                logger.info(f"✅ 找到用户详细信息: {result.get('name')} ({result.get('email')})")
                return dict(result)
            else:
                logger.warning(f"❌ 未找到open_id: {open_id}")
                return None

        except Exception as e:
            logger.exception(f"根据open_id获取用户详细信息失败 {open_id}: {e}")
            return None

    def update_user_folder_info(self, open_id: str, folder_token: str, folder_name: str) -> bool:
        try:
            sql = """
                UPDATE users 
                SET feishu_docs_folder_token = %s, feishu_docs_folder_name = %s 
                WHERE open_id = %s
            """
            execute_db_query(sql, (folder_token, folder_name, open_id), fetch=None, commit=True, database=Database.CHATBI)
            return True
        except Exception as e:
            logger.exception(f"更新用户文件夹信息失败 {open_id}: {e}")
            return False

    def get_user_folder_info(self, open_id: str) -> Optional[dict]:
        try:
            sql = """
                SELECT feishu_docs_folder_token, feishu_docs_folder_name
                FROM users
                WHERE open_id = %s
            """
            result = execute_db_query(sql, (open_id,), fetch='one', database=Database.CHATBI)
            if result:
                return {
                    "folder_token": result["feishu_docs_folder_token"],
                    "folder_name": result["feishu_docs_folder_name"],
                }
            return None
        except Exception as e:
            logger.exception(f"获取用户文件夹信息失败 {open_id}: {e}")
            return None

    def update_user_resigned_status(self, open_id: str, is_resigned: bool = True) -> bool:
        """更新用户的离职状态

        Args:
            open_id: 用户的open_id
            is_resigned: 是否已离职，默认为True

        Returns:
            bool: 是否更新成功
        """
        try:
            sql = """
                UPDATE users
                SET is_resigned = %s
                WHERE open_id = %s
            """
            execute_db_query(sql, (is_resigned, open_id), fetch=None, commit=True, database=Database.CHATBI)
            logger.info(f"用户离职状态更新成功: open_id={open_id}, is_resigned={is_resigned}")
            return True
        except Exception as e:
            logger.exception(f"更新用户离职状态失败: open_id={open_id}, is_resigned={is_resigned}, error={e}")
            return False
