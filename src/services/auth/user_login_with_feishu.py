import os
import requests
import jwt
import json
from datetime import datetime, timedelta
import urllib.parse
from flask import redirect, make_response, session, request, render_template, jsonify
from functools import wraps
from typing import Optional, Dict, Any

from src.services.xianmudb.query_service import execute_business_query
from src.utils.logger import logger
from src.utils.user_utils import get_api_token, get_valid_user_email
from src.utils.in_memory_cache import in_memory_cache
from src.db.connection import execute_db_query as _execute_db_query
from src.services.auth.user_session_service import user_session_service
from src.services.user_query_service import get_user_info_by_open_id

# 飞书应用的 App ID 和 App Secret
APP_ID = os.getenv("FEISHU_APP_ID")
APP_SECRET = os.getenv("FEISHU_APP_SECRET")
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key-change-in-production")

if not APP_ID or not APP_SECRET:
    raise ValueError("飞书应用的 App ID 和 App Secret 未配置")

# 本地调试：http://127.0.0.1:5700
HOST_NAME = os.getenv(
    "CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net"
)
MINUTES_TO_FORCE_REFRESH_TOKEN = int(os.getenv("MINUTES_TO_FORCE_REFRESH_TOKEN", 10))

logger.info(f"HOST_NAME: {HOST_NAME}")

scope = [
    "offline_access",
    "contact:user.email:readonly",
    "contact:department.base:readonly",
    "contact:user.base:readonly",
    "contact:user.employee:readonly",
    "contact:contact.base:readonly",
    "docs:document:import",
    "drive:drive",
    "drive:file",
    "drive:file:upload",
    "base:app:create",
    "bitable:app",
    "aily:file:write",
    "wiki:wiki:readonly",
    "docx:document:readonly",
    "docs:document.content:read",
    "im:message",
    "search:message",
]

scope_encoded = urllib.parse.quote_plus(" ".join(scope))


# 注意：用户表结构创建已移至 init.sql 文件
# 该模块专注于用户认证逻辑，不再包含建表逻辑

# Token刷新由后台服务统一处理，避免并发冲突


def generate_jwt_token(session_id: str) -> str:
    """
    生成JWT token
    """
    payload = {
        'session_id': session_id,
        'exp': datetime.utcnow() + timedelta(days=30),  # 30天过期
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')


def decode_jwt_token(token: str) -> Optional[dict]:
    """
    解码JWT token，返回payload字典
    """
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        logger.warning("JWT token已过期")
        return None
    except jwt.InvalidTokenError:
        logger.warning("无效的JWT token")
        return None




@in_memory_cache(expire_seconds=1200)  # 缓存20分钟
def get_user_admin_id(user_name, email):
    """
    根据用户名和邮箱获取admin_id
    """
    # 使用字符串格式化构建SQL查询，使用repr()来安全地转义字符串参数
    sql = f"""
    SELECT `admin_id`,`realname`,`username`
    FROM `admin`
    WHERE `is_disabled` = 0
      AND (username = {repr(email)} OR realname = {repr(user_name)})
    ORDER BY
      CASE WHEN username = {repr(email)} THEN 0 ELSE 1 END
    LIMIT 1;
    """
    # 这里使用execute_business_query，它连接的是业务数据库（MySQL），不是ChatBI数据库
    # 所以不需要考虑SQLite兼容性
    result = execute_business_query(sql)
    if result and result.data and len(result.data) > 0:
        logger.info(f"User {user_name} ({email}) found in database:{result}")
        return result.data[0][0]
    logger.exception(f"User {user_name} ({email}) not found in database.")
    return None


def get_user_info_from_feishu(access_token: str) -> Optional[Dict[str, Any]]:
    """
    从飞书获取用户信息
    """
    user_info_url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
    headers = {"Authorization": f"Bearer {access_token}"}

    try:
        user_info_response = requests.get(user_info_url, headers=headers, timeout=10)
        if user_info_response.status_code == 200:
            user_info = user_info_response.json().get("data")
            logger.info(f"获取到的用户信息: {user_info}")
            
            valid_email = get_valid_user_email(user_info)
            if not valid_email:
                logger.warning(f"用户信息中没有有效的email: {user_info}")
            user_info["email"] = valid_email
            
            # 获取头像信息，优先使用avatar_thumb
            avatar = user_info.get("avatar_thumb") or user_info.get("avatar_middle") or user_info.get("avatar_big")
            if avatar:
                user_info["avatar"] = avatar

            # 获取job title
            open_id = user_info.get("open_id")
            if open_id:
                job_title_url = f"https://open.feishu.cn/open-apis/contact/v3/users/{open_id}?department_id_type=open_department_id&user_id_type=open_id"
                job_title_response = requests.get(job_title_url, headers=headers, timeout=10)
                if job_title_response.status_code == 200:
                    job_title_data = job_title_response.json()
                    job_title = job_title_data.get("data", {}).get("user", {}).get("job_title")
                    if job_title:
                        user_info["job_title"] = job_title

            # 获取admin_id（如果获取失败，不阻止登录，后续会通过process_user_info补充）
            admin_id = get_user_admin_id(
                user_name=user_info.get("name"),
                email=valid_email
            )
            if admin_id:
                user_info["admin_id"] = admin_id
            else:
                logger.warning(f"初次登录时未获取到 admin_id，将在后续访问时重试, user_info:{user_info}")
                # 不再直接返回None，允许用户登录，admin_id会在后续通过process_user_info补充

            # 获取API token
            union_id = user_info.get("union_id")
            if union_id:
                api_token = get_api_token(union_id=union_id)
                user_info["summerfarm_api_token"] = api_token

            return user_info
        else:
            logger.exception(f"获取用户信息失败: {user_info_response.text}")
            return None
    except Exception as e:
        logger.exception(f"获取用户信息异常: {e}")
        return None


def upsert_user_info(user_info: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    插入或更新用户信息到数据库，并返回完整的用户信息
    
    Returns:
        Optional[Dict[str, Any]]: 更新后的完整用户信息，失败时返回None
    """
    open_id = user_info.get("open_id")

    try:
        # MySQL版本使用ON DUPLICATE KEY UPDATE，包含union_id字段
        # last_login_time 使用数据库的 DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        # 注意：登录过程中不更新first_level_department，因为登录时无法获取到准确的部门信息
        sql = """
        INSERT INTO `users` (`name`, `email`, `user_id`, `job_title`, `open_id`, `union_id`, `avatar`)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            `name`=VALUES(`name`),
            `email`=VALUES(`email`),
            `user_id`=VALUES(`user_id`),
            `job_title`=VALUES(`job_title`),
            `union_id`=VALUES(`union_id`),
            `avatar`=VALUES(`avatar`),
            `last_login_time`=CURRENT_TIMESTAMP;
        """
        # 处理avatar字段：如果是字符串URL直接使用，如果是字典则取avatar_640或avatar_240
        avatar_data = user_info.get("avatar")
        if avatar_data:
            if isinstance(avatar_data, str):
                # 如果是字符串URL，直接使用
                avatar_json = avatar_data
            elif isinstance(avatar_data, dict):
                # 如果是字典，则取avatar_640或avatar_240
                avatar_json = avatar_data.get("avatar_640", avatar_data.get("avatar_240"))
            else:
                avatar_json = None
        else:
            avatar_json = None

        if avatar_json and "{" in avatar_json:
            # '{"avatar_72": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp", "avatar_240": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp", "avatar_640": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp", "avatar_origin": "https://s1-imfile.feishucdn.com/static-resource/v1/v3_00ne_23f7a821-a2c2-490a-9048-cf0b89c0a46g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp"}'
            avatar_json = json.loads(avatar_json)
            avatar_json = avatar_json.get("avatar_640", avatar_json.get("avatar_240"))
        
        params = (
            user_info.get("name"),
            user_info.get("email"),
            user_info.get("user_id"),
            user_info.get("job_title"),
            open_id,
            user_info.get("union_id"),  # 添加union_id参数
            avatar_json
        )
        _execute_db_query(sql, params, fetch=None, commit=True)
            
        logger.info(f"用户信息upsert成功: open_id={open_id}, union_id={user_info.get('union_id')}")
        
        # 清除缓存并立即查询返回最新数据
        from src.utils.in_memory_cache import clear_cache
        clear_cache(get_user_info_by_open_id.__qualname__)
        
        # 直接从数据库查询最新的完整用户信息并返回
        return get_user_info_by_open_id(open_id)
        
    except Exception as e:
        logger.exception(f"用户信息upsert失败: {e}")
        return None


def _check_and_update_user_department(open_id: str, user_info: Optional[Dict[str, Any]] = None):
    """
    检查用户是否有部门信息，如果没有则获取并更新

    Args:
        open_id: 用户的open_id
        user_info: 可选的用户信息，如果提供则不需要重新查询
    """
    try:
        # 如果没有提供用户信息，则从数据库获取
        if not user_info:
            user_info = get_user_info_by_open_id(open_id)
            if not user_info:
                logger.warning(f"无法获取用户信息，跳过部门更新: open_id={open_id}")
                return

        # 检查用户是否已有有效的部门信息
        first_level_department = user_info.get("first_level_department")
        if first_level_department and len(first_level_department.strip()) > 1:
            logger.info(f"用户已有有效部门信息，跳过更新: open_id={open_id}, department={first_level_department}")
            return

        logger.info(f"用户缺少部门信息，开始获取并更新: open_id={open_id}")

        # 调用现有的方法来获取并更新部门信息
        from src.services.feishu.user_service import UserService
        success = UserService.update_user_first_level_department(open_id)

        if success:
            logger.info(f"登录后成功更新用户部门信息: open_id={open_id}")
        else:
            logger.warning(f"登录后更新用户部门信息失败: open_id={open_id}")

    except Exception as e:
        # 部门更新失败不应该影响登录流程
        logger.exception(f"登录后检查和更新用户部门信息时出错: open_id={open_id}, error={e}")


def update_user_first_level_department(open_id: str, first_level_department: str) -> bool:
    """
    根据open_id更新用户的一级部门信息

    Args:
        open_id: 用户的open_id
        first_level_department: 一级部门名称

    Returns:
        bool: 更新是否成功
    """
    try:
        # 验证first_level_department不为空且长度大于1
        if not first_level_department or not first_level_department.strip():
            logger.warning(f"一级部门名称为空，跳过更新: open_id={open_id}")
            return False

        if len(first_level_department.strip()) <= 1:
            logger.warning(f"一级部门名称长度无效（<=1），跳过更新: open_id={open_id}, department='{first_level_department}'")
            return False

        sql = """
        UPDATE `users`
        SET `first_level_department` = %s
        WHERE `open_id` = %s
        AND (`first_level_department` IS NULL OR `first_level_department` = '');
        """

        _execute_db_query(sql, (first_level_department.strip(), open_id), fetch=None)
        logger.info(f"用户一级部门更新成功: open_id={open_id}, first_level_department={first_level_department.strip()}")

        # 清除缓存
        from src.utils.in_memory_cache import clear_cache
        clear_cache(get_user_info_by_open_id.__qualname__)

        return True
    except Exception as e:
        logger.exception(f"用户一级部门更新失败: open_id={open_id}, error={e}")
        return False


def login(destination_path: str = None):
    """
    处理用户登录逻辑
    """
    def _build_feishu_auth_url(target_path: str) -> str:
        """构建飞书授权页面URL，包含完整的认证参数"""
        request_host = request.host_url.rstrip("/")
        redirect_uri = f"{request_host}/callback"
        auth_target = target_path if target_path else "/"
        return (
            f"https://accounts.feishu.cn/open-apis/authen/v1/authorize"
            f"?app_id={APP_ID}&redirect_uri={redirect_uri}"
            f"&response_type=code&scope={scope_encoded}"
            f"&state={urllib.parse.quote_plus(auth_target)}"
        )
    
    def _validate_and_redirect() -> tuple[bool, Optional[str]]:
        """
        验证当前JWT token的有效性并返回跳转目标
        返回: (验证成功标识, 跳转地址或None)
        """
        jwt_token = request.cookies.get("jwt_token")
        if not jwt_token:
            return False, None
        
        payload = decode_jwt_token(jwt_token)
        if not payload:
            return False, None
            
        session_id = payload.get('session_id')
        if not session_id:
            logger.warning("JWT token中没有session_id")
            return False, None
            
        user_session = user_session_service.get_session_by_id(session_id)
        if not user_session:
            logger.warning(f"无法获取用户session: session_id={session_id}")
            return False, None
            
        # 检查访问token是否过期
        if user_session.is_access_token_expired():
            logger.warning(f"Access token已过期，需要重新登录: session_id={session_id}")
            return False, None
            
        # 获取并验证用户信息
        user_info = get_user_info_by_open_id(user_session.open_id)
        if not user_info:
            logger.warning(f"无法获取用户信息: open_id={user_session.open_id}")
            return False, None

        # 【修复】调用process_user_info来确保admin_id等字段的完整性
        from src.services.feishu.user_service import UserService
        user_info = UserService.process_user_info(user_info)

        # 登录成功，设置用户会话并重定向
        session["user_info"] = user_info
        target_path = destination_path if destination_path else '/dashboard'
        return True, target_path

    # 主要登录处理逻辑
    is_authenticated, redirect_target = _validate_and_redirect()
    
    if is_authenticated:
        # 已认证用户，重定向到目标页面
        return redirect(redirect_target)
    else:
        # 需要重新授权，重定向到飞书登录页面
        auth_url = _build_feishu_auth_url(destination_path)
        logger.info(f"重定向到飞书授权URL: {auth_url}")
        return redirect(auth_url)


def callback():
    """
    处理飞书授权回调
    """
    request_host = request.host_url.rstrip("/")
    code = request.args.get("code")
    state = request.args.get("state")
    destination_path = urllib.parse.unquote_plus(state) if state else "/"
    
    if not code:
        logger.exception("未收到授权码")
        return render_template(
            "login_redirect.html", 
            success=False, 
            error_message="授权失败：未收到授权码"
        )

    logger.info(f"收到飞书授权码: {code}")
    redirect_uri = f"{request_host}/callback"

    try:
        # 请求access_token
        token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
        payload = {
            "grant_type": "authorization_code",
            "client_id": APP_ID,
            "client_secret": APP_SECRET,
            "code": code,
            "redirect_uri": redirect_uri,
        }

        response = requests.post(token_url, json=payload, timeout=10)
        token_response = response.json()
        logger.info(f"飞书token响应: {token_response}")

        if "error" in token_response or "access_token" not in token_response:
            error_msg = "飞书授权失败"
            logger.exception(f"获取飞书access token失败: {token_response}")
            return render_template(
                "login_redirect.html",
                success=False,
                error_message=error_msg
            )

        # 获取用户信息
        access_token = token_response["access_token"]
        refresh_token = token_response.get("refresh_token")
        user_info = get_user_info_from_feishu(access_token)

        if not user_info:
            return render_template(
                "login_redirect.html",
                success=False,
                error_message="您暂无权限访问，请联系管理员：唐鹏处理"
            )

        # 计算access token过期时间
        expires_at = datetime.now() + timedelta(hours=2)
        
        # 保存用户信息到数据库并获取完整的用户信息
        complete_user_info = upsert_user_info(user_info)
        if not complete_user_info:
            return render_template(
                "login_redirect.html",
                success=False,
                error_message="保存用户信息失败，请重试"
            )

        # 登录完成后，检查并更新用户的部门信息（传入已获取的用户信息避免重复查询）
        _check_and_update_user_department(user_info.get("open_id"), complete_user_info)

        # 创建用户session
        session_id = user_session_service.create_user_session(
            open_id=user_info.get("open_id"),
            refresh_token=refresh_token,
            access_token=access_token,
            access_token_expires_at=expires_at
        )

        if not session_id:
            return render_template(
                "login_redirect.html",
                success=False,
                error_message="创建用户会话失败，请重试"
            )

        # 不在这里刷新token，让后台服务统一处理token刷新
        logger.info(f"用户登录成功，session创建完成: session_id={session_id}")
        
        # 生成JWT token（包含session_id）
        jwt_token = generate_jwt_token(session_id)
        
        # 处理chat参数
        chat_param = request.args.get('chat')
        if chat_param:
            parsed_destination = urllib.parse.urlparse(destination_path)
            query_params = urllib.parse.parse_qs(parsed_destination.query)
            query_params['chat'] = [chat_param]
            new_query = urllib.parse.urlencode(query_params, doseq=True)
            destination_path = f"{parsed_destination.path}?{new_query}"

        # 设置JWT token到cookie
        final_redirect_url = f"{request_host}{destination_path}"
        resp = redirect(final_redirect_url)
        
        is_secure = request.scheme == "https"
        cookie_kwargs = {
            "secure": is_secure,
            "samesite": "None" if is_secure else None,
            "max_age": 30 * 24 * 60 * 60  # 30天
        }
        
        resp.set_cookie("jwt_token", jwt_token, **cookie_kwargs)
        session["user_info"] = user_info
        
        logger.info(f"用户登录成功: {user_info.get('name')}")
        return resp

    except requests.RequestException as e:
        logger.exception(f"请求飞书API时发生错误: {str(e)}")
        return render_template(
            "login_redirect.html",
            success=False,
            error_message=f"连接飞书服务器失败: {str(e)}"
        )
    except Exception as e:
        logger.exception(f"处理飞书回调时发生未知错误: {str(e)}")
        return render_template(
            "login_redirect.html",
            success=False,
            error_message=f"处理授权时发生错误: {str(e)}"
        )


def login_required(f):
    """
    登录验证装饰器
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        def _handle_unauthorized_response():
            """处理未授权情况的统一响应逻辑"""
            is_json_request = "application/json" in request.headers.get("Accept", "")
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 401)
            return redirect(build_login_redirect_url())
        
        def _validate_user_session():
            """验证用户会话的有效性，返回用户信息或None"""
            jwt_token = request.cookies.get("jwt_token")
            if not jwt_token:
                return None

            # 解码JWT token
            payload = decode_jwt_token(jwt_token)
            if not payload:
                return None

            session_id = payload.get('session_id')
            if not session_id:
                return None

            # 通过session服务获取用户session
            user_session = user_session_service.get_session_by_id(session_id)
            if not user_session:
                return None

            # 获取用户信息
            user_info = get_user_info_by_open_id(user_session.open_id)
            if not user_info:
                logger.warning(f"无法获取用户信息: open_id={user_session.open_id}")
                return None

            # 【修复】调用process_user_info来确保admin_id等字段的完整性
            from src.services.feishu.user_service import UserService
            user_info = UserService.process_user_info(user_info)

            # 不在这里刷新token，由后台服务统一处理
            # 只检查token是否已经过期，如果过期则需要重新登录
            if user_session.is_access_token_expired():
                logger.warning(f"Access token已过期，需要重新登录: session_id={session_id}")
                return None

            return user_info
        
        # 验证用户会话
        user_info = _validate_user_session()
        if not user_info:
            return _handle_unauthorized_response()
        
        # 将用户信息存储到session中
        session["user_info"] = user_info
        return f(*args, **kwargs)
    
    return decorated_function


def build_login_redirect_url():
    """
    构建登录重定向URL
    """
    full_url = request.url
    parsed_url = urllib.parse.urlparse(full_url)
    path_and_query = parsed_url.path
    if parsed_url.query:
        path_and_query += f"?{parsed_url.query}"
    next_param = urllib.parse.quote_plus(path_and_query)
    return f"{HOST_NAME}/login?next={next_param}"